<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Webinar extends Model
{
    use HasFactory;

    protected $fillable = [
        'events_id',
        'webinar_name',
        'webinar_code',
        'parent_webinar_code',
        'service_type',
        'webinar_type',
        'start_date',
        'end_date',
        'location',
        'host_type',
        'host_country',
        'webinar_cluster',
        'website_url',
        'backstage_url',
        'language',
        'translation_languages',
        'breakout_check',
        'survey',
        'polling',
        'certificate',
        'register_check'
    ];

    protected $casts = [
        'translation_languages' => 'array',
    ];

    public function event()
    {
        return $this->belongsTo(Event::class, 'events_id');
    }

    public function registers()
    {
        return $this->hasMany(Register::class);
    }
}
