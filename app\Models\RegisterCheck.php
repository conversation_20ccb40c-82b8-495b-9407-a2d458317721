<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegisterCheck extends Model
{
    use HasFactory;

    protected $fillable = [
        'register_id',
        'unsubscribe',
        'confirmation',
        'pending',
        'rejection',
        'reminder',
        'thank_you_email',
        'miss_you_email',
        'survey_submission',
        'polling',
        'q_a',
        'certificate',
        'login_status'
    ];

    protected $casts = [
        'confirmation' => 'array',
        'pending' => 'array',
        'rejection' => 'array',
        'reminder' => 'array',
        'thank_you_email' => 'array',
        'miss_you_email' => 'array',
    ];

    public function register()
    {
        return $this->belongsTo(Register::class);
    }
}
