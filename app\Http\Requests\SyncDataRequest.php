<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;

class SyncDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'events.code' => 'required|string|max:255',
            'events.company' => 'required|string|max:255',
            'events.region' => 'required|string|max:255',
            'events.series_name' => 'required|string|max:255',
            'events.name' => 'required|string|max:255',
            'events.product' => 'nullable|string|max:255',
            'events.business_owner_name' => 'nullable|string|max:255',
            'events.business_owner_email' => 'nullable|email|max:255',
            'events.webinars' => 'array',

            'events.webinars.*.webinar_name' => 'required|string|max:255',
            'events.webinars.*.webinar_code' => 'required|string|max:255',
            'events.webinars.*.parent_webinar_code' => 'nullable|string|max:255',
            'events.webinars.*.service_type' => 'required|string|max:255',
            'events.webinars.*.webinar_type' => 'required|string|max:255',
            /*'events.webinars.*.start_date' => 'required|date|before:events.webinars.end_date',*/
            /*'events.webinars.*.end_date' => 'required|date|after:events.webinars.start_date',*/
            'events.webinars.*.start_date' => 'nullable|string|max:255',
            'events.webinars.*.end_date' => 'nullable|string|max:255',
            'events.webinars.*.location' => 'string|max:255',
            'events.webinars.*.host_type' => 'required|string|max:255',
            'events.webinars.*.host_country' => 'required|string|max:255',
            'events.webinars.*.webinar_cluster' => 'nullable|string|max:255',
            'events.webinars.*.website_url' => 'nullable|string|max:255',
            'events.webinars.*.backstage_url' => 'nullable|string|max:255',
            'events.webinars.*.language' => 'nullable|max:255',
            'events.webinars.*.translation_languages' => 'nullable|array',
            'events.webinars.*.breakout_check' => 'required|boolean',
            'events.webinars.*.survey' => 'nullable|boolean',
            'events.webinars.*.polling' => 'nullable|boolean',
            'events.webinars.*.certificate' => 'nullable|boolean',
            'events.webinars.*.register_check' => 'required|boolean',
            'events.webinars.*.registers' => 'array',

            'events.webinars.*.registers.*.first_name' => 'nullable|max:255',
            'events.webinars.*.registers.*.last_name' => 'nullable|max:255',
            'events.webinars.*.registers.*.full_name' => 'nullable|max:255',
            'events.webinars.*.registers.*.email' => 'required|email|max:255',
            'events.webinars.*.registers.*.country' => 'nullable|max:255',
            'events.webinars.*.registers.*.speciality' => 'nullable|max:255',
            'events.webinars.*.registers.*.institution' => 'nullable|max:255',
            'events.webinars.*.registers.*.hcp_number' => 'nullable|max:255',
            'events.webinars.*.registers.*.hcp_phone_number' => 'nullable|max:255',
            'events.webinars.*.registers.*.campaign_tracking' => 'nullable|array',
            'events.webinars.*.registers.*.extra' => 'nullable|array',
            'events.webinars.*.registers.*.form_name' => 'nullable|max:255',

            'events.webinars.*.registers.*.register_checks.unsubscribe' => 'nullable|boolean',
            'events.webinars.*.registers.*.register_checks.confirmation' => 'nullable|array',
            'events.webinars.*.registers.*.register_checks.pending' => 'nullable|array',
            'events.webinars.*.registers.*.register_checks.rejection' => 'nullable|array',
            'events.webinars.*.registers.*.register_checks.reminder' => 'nullable|array',
            'events.webinars.*.registers.*.register_checks.thank_you_email' => 'nullable|array',
            'events.webinars.*.registers.*.register_checks.miss_you_email' => 'nullable|array',
            'events.webinars.*.registers.*.register_checks.survey_submission' => 'nullable|boolean',
            'events.webinars.*.registers.*.register_checks.polling' => 'nullable|boolean',
            'events.webinars.*.registers.*.register_checks.q_a' => 'nullable|boolean',
            'events.webinars.*.registers.*.register_checks.certificate' => 'nullable|boolean',
            'events.webinars.*.registers.*.register_checks.login_status' => 'nullable|boolean',
            'events.webinars.*.registers.*.register_stats.total_webinar_duration' => 'nullable|integer|min:0',
            'events.webinars.*.registers.*.register_stats.total_live_watching_time' => 'nullable|integer|min:0',
            'events.webinars.*.registers.*.register_stats.embed_duration' => 'nullable|array',

            /*'events.webinars.registers.*.register_stats.embed_duration.*.embed' =>
                'required_with:events.webinars.registers.*.register_stats.embed_duration|string|max:255',*/
            /*'events.webinars.registers.*.register_stats.embed_duration.*.duration' =>
                'required_with:events.webinars.registers.*.register_stats.embed_duration|integer|min:0',*/
        ];
    }

    public function messages()
    {
        return [
            'required' =>
                'The :attribute field is required.',
            'email' =>
                'The :attribute must be a valid email address.',
            'date' =>
                'The :attribute must be a valid date.',
            'url' =>
                'The :attribute must be a valid URL.',
            'before' =>
                'The :attribute must be a date before :date.',
            'after' =>
                'The :attribute must be a date after :date.',
            'boolean' =>
                'The :attribute field must be true or false.',
            'array' =>
                'The :attribute must be an array.',
        ];
    }
}
