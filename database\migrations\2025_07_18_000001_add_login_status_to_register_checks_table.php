<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('register_checks', function (Blueprint $table) {
            $table->boolean('login_status')->default(false)->after('certificate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('register_checks', function (Blueprint $table) {
            $table->dropColumn('login_status');
        });
    }
};
