<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Register extends Model
{
    use HasFactory;

    protected $fillable = [
        'webinar_id',
        'first_name',
        'last_name',
        'full_name',
        'email',
        'country',
        'speciality',
        'institution',
        'hcp_number',
        'hcp_phone_number',
        'campaign_tracking',
        'extra',
        'form_name'
    ];

    protected $casts = [
        'campaign_tracking' => 'array',
        'extra' => 'array',
    ];

    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    public function registerCheck()
    {
        return $this->hasOne(RegisterCheck::class);
    }

    public function registerStats()
    {
        return $this->hasOne(RegisterStat::class);
    }
}
