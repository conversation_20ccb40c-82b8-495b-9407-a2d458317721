name: Testing

on:
  push:
    branches:
      - '*'
  pull_request:

jobs:
  Test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:17
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: datalake_testing
          POSTGRES_USER: datalake
          POSTGRES_PASSWORD: datalake
        options: >-
          --health-cmd="pg_isready -U datalake"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: Checkout Code
        uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          tools: composer

      - name: Install Dependencies
        run: composer install --prefer-dist --no-progress --no-suggest

      - name: Run PHP CodeSniffer
        run: ./vendor/bin/phpcs --standard=PSR12 app/ database/ routes/

      - name: Set up Testing Database Configuration
        run: |
          cp .env.testing .env

      - name: Run Migrations
        run: php artisan migrate --env=testing --force

      #- name: Run Tests
      #  run: php artisan test --env=testing --stop-on-failure
