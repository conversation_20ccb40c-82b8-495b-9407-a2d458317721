<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

class BasicAuthentication
{
    private ?string $USER;
    private ?string $PASS;

    public function __construct()
    {
        $this->USER = env('BASIC_AUTH_USER');
        $this->PASS = env('BASIC_AUTH_PASS');
    }

    public function handle(Request $request, Closure $next)
    {
        if (is_null($this->USER) || is_null($this->PASS)) {
            return $next($request);
        }

        if ($request->hasHeader('Authorization') === false) {
            header('WWW-Authenticate: Basic realm="My Realm"');
            exit;
        }

        $credentials = base64_decode(substr($request->header('Authorization'), 6));
        list($username, $password) = explode(':', $credentials);

        if ($username !== $this->USER || $password !== $this->PASS) {
            return response()->json(['error' => 'Unauthorized'], Response::HTTP_UNAUTHORIZED);
        }

        return $next($request);
    }
}
