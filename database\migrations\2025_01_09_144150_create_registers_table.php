<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('registers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('webinar_id')->constrained('webinars')->cascadeOnDelete();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('full_name');
            $table->string('email');
            $table->string('country');
            $table->string('speciality');
            $table->string('institution');
            $table->string('hcp_number');
            $table->string('hcp_phone_number')->nullable();
            $table->json('campaign_tracking')->nullable();
            $table->json('extra')->nullable();
            $table->string('form_name')->nullable();
            $table->timestamps();

            $table->unique(['webinar_id', 'email', 'form_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('registers');
    }
};
