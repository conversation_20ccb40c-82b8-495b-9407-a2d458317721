<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegisterStat extends Model
{
    use HasFactory;

    protected $fillable = [
        'register_id',
        'total_webinar_duration',
        'total_live_watching_time',
        'embed_duration'
    ];

    protected $casts = [
        'embed_duration' => 'array',
    ];

    public function register()
    {
        return $this->belongsTo(Register::class);
    }
}
