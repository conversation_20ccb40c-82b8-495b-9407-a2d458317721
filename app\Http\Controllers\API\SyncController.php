<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\SyncDataRequest;
use App\Http\Resources\SyncResource;
use App\Http\Services\SyncService;

class SyncController extends Controller
{
    public function index(SyncDataRequest $request)
    {
        set_time_limit(0);
        ini_set('memory_limit', '-1');

        $sync = new SyncService($request);
        $result = $sync->make();

        return new SyncResource($result);
    }
}
