Table events {
  id integer [primary key]
  company varchar
  region varchar
  series_name varchar
  name varchar
  product varchar
  business_owner_name varchar
  business_owner_email varchar
}

Table webinars {
  id integer [primary key]
  events_id integer [ref: > events.id]
  webinar_name varchar
  webinar_code varchar
  parent_webinar_code varchar
  service_type varchar
  webinar_type varchar
  start_date datetime
  end_date datetime
  location varchar
  host_type varchar
  host_country varchar
  webinar_cluster varchar
  website_url varchar
  backstage_url varchar
  language varchar
  translation_languages json
  breakout_check boolean
  register_check boolean
}

Table registers {
  id integer [primary key]
  webinar_id integer [ref: > webinars.id]
  first_name varchar
  last_name varchar
  full_name varchar
  email varchar
  country varchar
  speciality varchar
  institution varchar
  hcp_number varchar
  hcp_phone_number varchar
  campaign_tracking json
  extra json
  form_name varchar
}

Table register_checks {
  id integer [primary key]
  register_id integer [ref: > registers.id]
  unsubscribe boolean
  confirmation json [note: 'Expected structure: { status: boolean, confirmation_primary_cta: boolean }']
  pending json [note: 'Expected structure: { status: boolean, pending_primary_cta: boolean }']
  rejection json [note: 'Expected structure: { status: boolean, rejection_primary_cta: boolean }']
  reminder json [note: 'Expected structure: [ { name: string, reminder_primary_cta: int }]']
  thank_you_email json [note: 'Expected structure: { status: boolean, email_primary_cta: boolean }']
  miss_you_email json [note: 'Expected structure: { status: boolean, email_primary_cta: boolean }']
  survey_submission boolean
  polling boolean
  q_a boolean
  certificate boolean
  login_status boolean
}

Table register_stats {
  id integer [primary key]
  register_id integer [ref: > registers.id]
  total_webinar_duration int
  total_live_watching_time int
  embed_duration json [note: 'Expected structure: [ { embed: string, duration: int }]']
}
