<?php

namespace App\Http\Services;

use App\Http\Requests\SyncDataRequest;
use App\Models\Event;
use Illuminate\Support\Facades\DB;

class SyncService
{
    protected array $data;

    public function __construct(SyncDataRequest $request)
    {
        $this->data = $request->validated();
    }

    public function make()
    {
        set_time_limit(0);

        try {
            $result = DB::transaction(function () {
                $eventMapping = $this->eventMapping();
                $event = Event::updateOrCreate(
                    ['code' => $eventMapping['code']],
                    $eventMapping
                );

                $webinarMappings = $this->webinarsMapping();
                $webinars = [];
                foreach ($webinarMappings as $webinarMapping) {
                    $webinar = $event->webinars()->updateOrCreate(
                        ['webinar_code' => $webinarMapping['webinar_code']],
                        $webinarMapping
                    );
                    $registers = [];
                    foreach ($this->registersMapping($webinarMapping['webinar_code']) as $item) {
                        $register_checks = $item['register_checks'];
                        $register_stats = $item['register_stats'];
                        unset($item['register_checks'], $item['register_stats']);

                        $register = $webinar->registers()->updateOrCreate(
                            [
                                'webinar_id' => $webinar->id,
                                'email' => $item['email'],
                                'form_name' => $item['form_name'],
                            ],
                            $item
                        );
                        $register->registerCheck()->updateOrCreate(
                            ['register_id' => $register->id],
                            $register_checks
                        );
                        $register->registerStats()->updateOrCreate(
                            ['register_id' => $register->id],
                            $register_stats
                        );

                        $registers[] = $register->toArray();
                    }
                    $webinars[] = [
                        'webinar' => $webinar->toArray(),
                        'registers' => $registers,
                    ];
                }
                return [
                    'events' => $event->toArray(),
                    'webinars' => $webinars,
                ];
            });
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'error' => $e->getMessage(),
            ];
        }
        return $result;
    }

    private function eventMapping()
    {
        return [
            'code' => $this->data['events']['code'],
            'company' => $this->data['events']['company'],
            'region' => $this->data['events']['region'],
            'series_name' => $this->data['events']['series_name'],
            'name' => $this->data['events']['name'],
            'product' => $this->data['events']['product'],
            'business_owner_name' => $this->data['events']['business_owner_name'] ?? " ",
            'business_owner_email' => $this->data['events']['business_owner_email'] ?? " ",
        ];
    }

    private function webinarsMapping()
    {
        $webinars = [];
        foreach ($this->data['events']['webinars'] as $webinar) {
            $webinars[] = [
                'webinar_name' => $webinar['webinar_name'],
                'webinar_code' => $webinar['webinar_code'],
                'parent_webinar_code' => $webinar['parent_webinar_code'] ?? null,
                'service_type' => $webinar['service_type'],
                'webinar_type' => $webinar['webinar_type'],
                'start_date' => $webinar['start_date'],
                'end_date' => $webinar['end_date'],
                'location' => $webinar['location'],
                'host_type' => $webinar['host_type'],
                'host_country' => $webinar['host_country'],
                'webinar_cluster' => $webinar['webinar_cluster'],
                'website_url' => $webinar['website_url'],
                'backstage_url' => $webinar['backstage_url'] ?? null,
                'language' => $webinar['language'],
                'translation_languages' => $webinar['translation_languages'] ?? null,
                'breakout_check' => $webinar['breakout_check'],
                'survey' => $webinar['survey'],
                'polling' => $webinar['polling'],
                'certificate' => $webinar['certificate'],
                'register_check' => $webinar['register_check'],
            ];
        }
        return $webinars;
    }

    private function registersMapping($webinar_code)
    {
        $registers = [];
        foreach ($this->data['events']['webinars'] as $webinar) {
            if ($webinar['webinar_code'] === $webinar_code) {
                foreach ($webinar['registers'] as $register) {
                    $registers[] = [
                        'first_name' => $register['first_name'],
                        'last_name' => $register['last_name'],
                        'full_name' => $register['full_name'],
                        'email' => $register['email'],
                        'country' => $register['country'],
                        'speciality' => $register['speciality'],
                        'institution' => $register['institution'],
                        'hcp_number' => $register['hcp_number'],
                        'hcp_phone_number' => $register['hcp_phone_number'],
                        'campaign_tracking' => $register['campaign_tracking'],
                        'extra' => $register['extra'],
                        'form_name' => $register['form_name'],

                        'register_checks' => $this->registerChecksMapping($register['register_checks']),
                        'register_stats' => $this->registerStatsMapping($register['register_stats']),
                    ];
                }
            }
        }
        return $registers;
    }

    private function registerChecksMapping($register_checks)
    {
        return [
            'unsubscribe' => $register_checks['unsubscribe'],
            'confirmation' => $register_checks['confirmation']['status'],
            'pending' => $register_checks['pending']['status'],
            'rejection' => $register_checks['rejection']['status'],
            'reminder' => $register_checks['reminder'],
            'thank_you_email' => $register_checks['thank_you_email']['status'],
            'miss_you_email' => $register_checks['miss_you_email']['status'],
            'survey_submission' => $register_checks['survey_submission'],
            'polling' => $register_checks['polling'],
            'q_a' => $register_checks['q_a'],
            'certificate' => $register_checks['certificate'],
            'login_status' => $register_checks['login_status'] ?? false,
        ];
    }

    private function registerStatsMapping($register_stats)
    {
        return [
            'total_webinar_duration' => $register_stats['total_webinar_duration'],
            'total_live_watching_time' => $register_stats['total_live_watching_time'],
            'embed_duration' => $register_stats['embed_duration'],
        ];
    }
}
