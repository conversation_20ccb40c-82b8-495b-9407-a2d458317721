<?php

namespace Tests\Unit;

use App\Models\Event;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EventTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test to check if the event has many webinars.
     */
    #[Test] public function it_returns_webinars_by_event_name()
    {
        $event = Event::create([
            'code' => 'TC2025',
            'company' => 'TechCorp',
            'region' => 'EMEA',
            'series_name' => 'Innovation Series',
            'name' => 'Tech Conference 2025',
            'product' => 'ProductX',
            'business_owner_name' => '<PERSON>',
            'business_owner_email' => '<EMAIL>',
        ]);

        $event->webinars()->create([
            'webinar_name' => 'AI and the Future',
            'webinar_code' => 'AIF2025',
            'service_type' => 'webinar',
            'webinar_type' => 'live',
            'start_date' => now(),
            'end_date' => now()->addHour(),
            'location' => 'Online',
            'host_type' => 'internal',
            'host_country' => 'USA',
            'webinar_cluster' => 'AI',
            'website_url' => 'https://techcorp.com/webinars/aif2025',
            'language' => 'English',
            'breakout_check' => true,
            'register_check' => true,
        ]);

        $event->webinars()->create([
            'webinar_name' => 'Data Science Insights',
            'webinar_code' => 'DSI2025',
            'service_type' => 'webinar',
            'webinar_type' => 'live',
            'start_date' => now()->addDay(),
            'end_date' => now()->addDay()->addHour(),
            'location' => 'Online',
            'host_type' => 'internal',
            'host_country' => 'USA',
            'webinar_cluster' => 'Data Science',
            'website_url' => 'https://techcorp.com/webinars/dsi2025',
            'language' => 'English',
            'breakout_check' => true,
            'survey' => true,
            'polling' => true,
            'certificate' => true,
            'register_check' => true,
        ]);

        $webinars = $event->getWebinarsByEventName('AI and the Future');

        $this->assertCount(1, $webinars);
        $this->assertTrue($webinars->contains('webinar_name', 'AI and the Future'));
        $this->assertFalse($webinars->contains('webinar_name', 'Data Science Insights'));
    }
}
