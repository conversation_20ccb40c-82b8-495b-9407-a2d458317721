<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="621px" height="166px" viewBox="-0.5 -0.5 621 166" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36&quot; version=&quot;26.0.4&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;0&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1434&quot; dy=&quot;819&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;a_project&quot; value=&quot;Vistream Engine&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;260&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;b_project&quot; value=&quot;ViPortal&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d6b656;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;375&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;c_project&quot; value=&quot;Datalake Collector&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;339&quot; y=&quot;300&quot; width=&quot;150&quot; height=&quot;75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;superset&quot; value=&quot;Superset&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;312.5&quot; width=&quot;120&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow_a_to_c&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;strokeColor=#000000;flowAnimation=1;&quot; parent=&quot;1&quot; source=&quot;a_project&quot; target=&quot;c_project&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow_b_to_c&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;strokeColor=#000000;entryX=0.5;entryY=1;entryDx=0;entryDy=0;flowAnimation=1;shape=link;&quot; parent=&quot;1&quot; source=&quot;b_project&quot; target=&quot;a_project&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;150&quot; y=&quot;350&quot; /&gt;&#10;              &lt;mxPoint x=&quot;150&quot; y=&quot;350&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow_c_to_superset&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;strokeColor=#000000;flowAnimation=1;shape=link;&quot; parent=&quot;1&quot; source=&quot;c_project&quot; target=&quot;superset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs><style>@keyframes ge-flow-animation-VZUohNrE35JBND5iR2gS {&#xa;  to {&#xa;    stroke-dashoffset: 0;&#xa;  }&#xa;}</style></defs><rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="a_project"><g><rect x="0" y="0" width="100" height="50" rx="7.5" ry="7.5" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 25px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Vistream Engine</div></div></div></foreignObject><image x="1" y="18.5" width="98" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="b_project"><g><rect x="0" y="115" width="100" height="50" rx="7.5" ry="7.5" fill="#ffe6cc" stroke="#d6b656" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 140px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ViPortal</div></div></div></foreignObject><image x="1" y="133.5" width="98" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="c_project"><g><rect x="239" y="40" width="150" height="75" rx="11.25" ry="11.25" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 78px; margin-left: 240px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Datalake Collector</div></div></div></foreignObject><image x="240" y="71.5" width="148" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="superset"><g><rect x="500" y="52.5" width="120" height="50" rx="7.5" ry="7.5" fill="#fff2cc" stroke="#d6b656" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 78px; margin-left: 501px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Superset</div></div></div></foreignObject><image x="501" y="71.5" width="118" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="arrow_a_to_c"><g><path d="M 100 25 L 159.5 25 Q 169.5 25 169.5 35 L 169.5 67.5 Q 169.5 77.5 179.5 77.5 L 232.63 77.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-VZUohNrE35JBND5iR2gS; stroke-dashoffset: 16;"/><path d="M 237.88 77.5 L 230.88 81 L 232.63 77.5 L 230.88 74 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/></g></g><g data-cell-id="arrow_b_to_c"><g><path d="M 48 115 L 48 90 L 48 50 M 52 50 L 52 90 Q 52 90 52 90 L 52 115 M 52 50" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-VZUohNrE35JBND5iR2gS; stroke-dashoffset: 16;"/></g></g><g data-cell-id="arrow_c_to_superset"><g><path d="M 389 75.5 L 500 75.5 M 500 79.5 L 389 79.5 M 500 79.5" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-VZUohNrE35JBND5iR2gS; stroke-dashoffset: 16;"/></g></g></g></g></g></svg>