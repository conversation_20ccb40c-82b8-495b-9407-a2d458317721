<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class SyncDataTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /**
     * Test the sync endpoint creates data.
     */
    public function test_sync_endpoint_creates_data(): void
    {
        $data = [
            'events' => [
                'code' => $this->faker->unique()->bothify('EV-####'),
                'company' => $this->faker->company,
                'region' => $this->faker->randomElement(['EMEA', 'APAC', 'AMERICAS']),
                'series_name' => $this->faker->word,
                'name' => $this->faker->sentence(3),
                'product' => $this->faker->word,
                'business_owner_name' => $this->faker->name,
                'business_owner_email' => $this->faker->safeEmail,
                'webinars' => [
                    'webinar_name' => $this->faker->sentence(2),
                    'webinar_code' => $this->faker->unique()->bothify('WEB-####'),
                    'service_type' => $this->faker->randomElement(['webinar', 'meeting']),
                    'webinar_type' => $this->faker->randomElement(['live', 'recorded']),
                    'start_date' => now()->addDays(1),
                    'end_date' => now()->addDays(1)->addHours(2),
                    'location' => $this->faker->city,
                    'host_type' => $this->faker->randomElement(['internal', 'external']),
                    'host_country' => $this->faker->country,
                    'webinar_cluster' => $this->faker->word,
                    'website_url' => $this->faker->url,
                    'language' => $this->faker->randomElement(['en', 'fr', 'de']),
                    'breakout_check' => $this->faker->boolean,
                    'survey' => $this->faker->boolean,
                    'polling' => $this->faker->boolean,
                    'certificate' => $this->faker->boolean,
                    'register_check' => $this->faker->boolean,
                    'registers' => [
                        [
                            'session_id' => 1,
                            'first_name' => $this->faker->firstName,
                            'last_name' => $this->faker->lastName,
                            'full_name' => $this->faker->name,
                            'email' => $this->faker->safeEmail,
                            'country' => $this->faker->country,
                            'speciality' => $this->faker->word,
                            'institution' => $this->faker->company,
                            'hcp_number' => (string)$this->faker->randomNumber(6),
                            'hcp_phone_number' => $this->faker->phoneNumber,
                            'campaign_tracking' => [
                                'source' => 'email',
                                'medium' => 'newsletter',
                                'campaign' => 'TestCampaign'
                            ],
                            'extra' => [
                                'notes' => 'Test note'
                            ],
                            'form_name' => $this->faker->word,
                            'register_checks' => [
                                'unsubscribe' => false,
                                'confirmation' => [
                                    'status' => true,
                                    'confirmation_primary_cta' => true
                                ],
                                'pending' => [
                                    'status' => false,
                                    'pending_primary_cta' => false
                                ],
                                'rejection' => [
                                    'status' => false,
                                    'rejection_primary_cta' => false
                                ],
                                'reminder' => [
                                    ['name' => 'First Reminder', 'reminder_primary_cta' => 1],
                                    ['name' => 'Second Reminder', 'reminder_primary_cta' => 2]
                                ],
                                'thank_you_email' => [
                                    'status' => true,
                                    'email_primary_cta' => true
                                ],
                                'miss_you_email' => [
                                    'status' => false,
                                    'email_primary_cta' => false
                                ],
                                'survey_submission' => true,
                                'polling' => false,
                                'q_a' => true,
                                'certificate' => true
                            ],
                            'register_stats' => [
                                'total_webinar_duration' => 120,
                                'total_live_watching_time' => 90,
                                'embed_duration' => [
                                    ['embed' => 'Main Presentation', 'duration' => 60],
                                    ['embed' => 'Q&A Session', 'duration' => 30]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $response = $this->postJson('/api/v1/sync', $data, [
            'Authorization' => 'Bearer ' . base64_encode(env('BASIC_AUTH_USER') . ':' . env('BASIC_AUTH_PASS'))
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('events', [
            'name' => $data['events']['name'],
            'business_owner_email' => $data['events']['business_owner_email']
        ]);

        $this->assertDatabaseHas('webinars', [
            'webinar_code' => $data['events']['webinars']['webinar_code']
        ]);

        foreach ($data['events']['webinars']['registers'] as $register) {
            $this->assertDatabaseHas('registers', [
                'email' => $register['email'],
                'form_name' => $register['form_name']
            ]);

            $this->assertDatabaseHas('register_checks', [
                'unsubscribe' => $register['register_checks']['unsubscribe'],
                'survey_submission' => $register['register_checks']['survey_submission'],
                'polling' => $register['register_checks']['polling'],
                'q_a' => $register['register_checks']['q_a'],
                'certificate' => $register['register_checks']['certificate']
            ]);

            $this->assertDatabaseHas('register_stats', [
                'total_webinar_duration' => $register['register_stats']['total_webinar_duration'],
                'total_live_watching_time' => $register['register_stats']['total_live_watching_time']
            ]);
        }
    }
}
