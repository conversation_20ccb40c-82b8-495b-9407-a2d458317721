<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SyncResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'status' => $this['error'] ?? 'success',
            'message' => $this['message'] ?? 'Data synced successfully',
            'synced_data' => [
                'event' => $this->when(!empty($this['events']), $this['events']),
                'webinars' => $this->when(!empty($this['webinars']), $this['webinars']),
            ],
            'timestamp' => now()->toDateTimeString(),
        ];
    }
}
