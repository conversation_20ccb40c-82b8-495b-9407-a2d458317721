<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'company',
        'region',
        'series_name',
        'name',
        'product',
        'business_owner_name',
        'business_owner_email'
    ];

    public function webinars()
    {
        return $this->hasMany(Webinar::class, 'events_id');
    }

    public function getWebinarsByEventName($eventName)
    {
        return $this->webinars()->where('webinar_name', $eventName)->get();
    }
}
