<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webinars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('events_id')->constrained('events')->cascadeOnDelete();
            $table->string('webinar_name');
            $table->string('webinar_code')->unique();
            $table->string('service_type');
            $table->string('webinar_type');
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->string('location')->nullable();
            $table->string('host_type');
            $table->string('host_country');
            $table->string('webinar_cluster')->nullable();
            $table->string('website_url')->nullable();
            $table->string('language');
            $table->boolean('breakout_check')->default(false);
            $table->boolean('survey')->default(false);
            $table->boolean('polling')->default(false);
            $table->boolean('certificate')->default(false);
            $table->boolean('register_check')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webinars');
    }
};
