<?php

use App\Http\Middleware\BasicAuthentication;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware(BasicAuthentication::class)
                ->prefix('api/v1')
                ->group(base_path('routes/api/v1.php'));
        }
    )
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
