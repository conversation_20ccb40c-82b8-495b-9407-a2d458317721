<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('register_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('register_id')->constrained('registers')->cascadeOnDelete();
            $table->boolean('unsubscribe')->default(false);
            $table->json('confirmation')->nullable();
            $table->json('pending')->nullable();
            $table->json('rejection')->nullable();
            $table->json('reminder')->nullable();
            $table->json('thank_you_email')->nullable();
            $table->json('miss_you_email')->nullable();
            $table->boolean('survey_submission')->default(false);
            $table->boolean('polling')->default(false);
            $table->boolean('q_a')->default(false);
            $table->boolean('certificate')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('register_checks');
    }
};
